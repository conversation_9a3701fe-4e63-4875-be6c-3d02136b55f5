// API route to serve sitemap with proper headers
export default function handler(req, res) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
  <!-- CJ Jutba Portfolio - Comprehensive Sitemap for SEO -->
  <url>
    <loc>https://cjjutba.site/</loc>
    <lastmod>2025-07-24T10:14:38+00:00</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://cjjutba.site/about</loc>
    <lastmod>2025-07-24T10:14:38+00:00</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>https://cjjutba.site/projects</loc>
    <lastmod>2025-07-24T10:14:38+00:00</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://cjjutba.site/skills</loc>
    <lastmod>2025-07-24T10:14:38+00:00</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>https://cjjutba.site/contact</loc>
    <lastmod>2025-07-24T10:14:38+00:00</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`;

  // Set proper headers for XML sitemap
  res.setHeader('Content-Type', 'application/xml; charset=utf-8');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
  res.status(200).send(sitemap);
}
